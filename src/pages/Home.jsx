import React, { useState, useEffect } from 'react'
import {
  ArrowRight, Shield, Eye, Zap, Users, CheckCircle, Star,
  Code, Search, AlertTriangle, TrendingUp, Award, Clock,
  Globe, Lock, Cpu, Database, FileText, BarChart3
} from 'lucide-react'

const Home = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0)

  const features = [
    {
      icon: Eye,
      title: '智能扫描',
      description: 'AI驱动的代码分析，如鹰眼般敏锐发现潜在安全隐患',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Zap,
      title: '极速检测',
      description: '毫秒级响应，快速识别威胁并提供精确定位',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Shield,
      title: '深度防护',
      description: '多层次安全分析，提供专业修复建议和最佳实践',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Users,
      title: '团队协作',
      description: '支持团队协作，实时共享安全报告和修复进度',
      color: 'from-purple-500 to-pink-500'
    }
  ]

  const stats = [
    { number: '1000+', label: '审计项目', icon: FileText },
    { number: '50K+', label: '发现漏洞', icon: AlertTriangle },
    { number: '99.9%', label: '准确率', icon: Award },
    { number: '24/7', label: '监控服务', icon: Clock }
  ]

  const technologies = [
    { name: 'Solidity', icon: Code, description: '智能合约静态分析' },
    { name: 'Rust', icon: Cpu, description: '高性能检测引擎' },
    { name: 'AI/ML', icon: Search, description: '机器学习威胁识别' },
    { name: 'Cloud', icon: Globe, description: '云原生架构' }
  ]

  const testimonials = [
    {
      name: '张伟',
      role: 'DeFi项目技术总监',
      company: 'CryptoFinance',
      content: 'ChainHawk帮助我们发现了17个关键安全漏洞，避免了潜在的重大损失。专业、高效、值得信赖！',
      avatar: '👨‍💻'
    },
    {
      name: '李小雨',
      role: '区块链安全专家',
      company: 'BlockSafe',
      content: '作为安全专家，我强烈推荐ChainHawk。它的检测精度和报告质量都达到了行业顶尖水平。',
      avatar: '👩‍💼'
    },
    {
      name: '王强',
      role: 'NFT平台创始人',
      company: 'ArtChain',
      content: '使用ChainHawk后，我们的智能合约安全性得到了显著提升，用户信任度也大幅增加。',
      avatar: '🧑‍🎨'
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  const services = [
    {
      title: '智能合约审计',
      description: '全面的智能合约代码审计服务，识别潜在的安全漏洞和逻辑错误',
      features: ['静态代码分析', '动态测试', '形式化验证', '详细报告'],
      icon: Code,
      color: 'from-blue-600 to-blue-800'
    },
    {
      title: '实时监控',
      description: '7x24小时实时监控您的智能合约，及时发现异常行为',
      features: ['实时告警', '异常检测', '性能监控', '风险评估'],
      icon: BarChart3,
      color: 'from-green-600 to-green-800'
    },
    {
      title: '安全咨询',
      description: '专业的区块链安全咨询服务，为您的项目提供全方位安全指导',
      features: ['架构设计', '最佳实践', '风险评估', '培训服务'],
      icon: Shield,
      color: 'from-purple-600 to-purple-800'
    }
  ]

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0">
          {/* Grid Pattern */}
          <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

          {/* Floating Elements */}
          <div className="absolute top-20 left-10 w-6 h-6 bg-cyan-400/20 rounded-full animate-float"></div>
          <div className="absolute top-40 right-20 w-4 h-4 bg-blue-400/30 rounded-full animate-float" style={{animationDelay: '1s'}}></div>
          <div className="absolute bottom-32 left-1/4 w-8 h-8 bg-indigo-400/25 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
          <div className="absolute bottom-20 right-1/3 w-5 h-5 bg-purple-400/20 rounded-full animate-float" style={{animationDelay: '3s'}}></div>

          {/* Gradient Orbs */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="section-container relative z-10">
          <div className="text-center">
            <div className="mb-12 animate-slide-up">
              {/* Logo Animation */}
              <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-2xl mb-8 shadow-2xl shadow-blue-500/25 animate-bounce">
                <Shield className="w-12 h-12 text-white" />
              </div>

              {/* Main Title */}
              <h1 className="text-6xl md:text-8xl font-bold text-white mb-6 tracking-tight">
                Chain<span className="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">Hawk</span>
              </h1>

              {/* Subtitle */}
              <div className="mb-6">
                <p className="text-2xl md:text-3xl text-cyan-100 mb-2 font-light">
                  区块链安全检测专家
                </p>
                <div className="w-24 h-1 bg-gradient-to-r from-cyan-400 to-blue-400 mx-auto rounded-full"></div>
              </div>

              {/* Description */}
              <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
                运用AI驱动的智能分析技术，如鹰眼般敏锐地识别智能合约安全漏洞，
                <br className="hidden md:block" />
                为您的区块链项目提供全方位的安全保障
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in mb-16">
              <button className="group bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold text-lg px-10 py-4 rounded-xl transition-all duration-300 shadow-xl shadow-blue-500/25 hover:shadow-2xl hover:shadow-blue-500/40 transform hover:-translate-y-1">
                立即开始检测
                <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </button>
              <button className="border-2 border-white/30 text-white hover:bg-white hover:text-slate-900 font-semibold text-lg px-10 py-4 rounded-xl transition-all duration-300 backdrop-blur-sm">
                观看演示
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              {stats.map((stat, index) => (
                <div key={index} className="group">
                  <div className="flex items-center justify-center mb-2">
                    <stat.icon className="w-6 h-6 text-cyan-400 mr-2" />
                    <div className="text-3xl md:text-4xl font-bold text-white group-hover:text-cyan-400 transition-colors">
                      {stat.number}
                    </div>
                  </div>
                  <div className="text-slate-400 text-sm uppercase tracking-wider">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gradient-to-b from-slate-50 to-white">
        <div className="section-container">
          <div className="text-center mb-20">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl mb-6">
              <Eye className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-5xl font-bold text-slate-900 mb-6">
              为什么选择 <span className="bg-gradient-to-r from-cyan-600 to-blue-600 bg-clip-text text-transparent">ChainHawk</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
              我们以鹰的锐利目光守护区块链世界的安全，运用前沿AI技术为开发者和项目方提供全方位的代码安全检测服务
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="group relative bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-slate-100">
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-500`}></div>

                {/* Icon */}
                <div className={`relative w-14 h-14 bg-gradient-to-br ${feature.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <feature.icon className="w-7 h-7 text-white" />
                </div>

                {/* Content */}
                <div className="relative">
                  <h3 className="text-xl font-bold text-slate-900 mb-4 group-hover:text-slate-800">
                    {feature.title}
                  </h3>
                  <p className="text-slate-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>

                {/* Hover Effect Border */}
                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-20 transition-opacity duration-500 -z-10 blur-xl`}></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technology Stack Section */}
      <section className="py-24 bg-slate-900">
        <div className="section-container">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">
              前沿技术驱动
            </h2>
            <p className="text-xl text-slate-400 max-w-3xl mx-auto">
              基于最新的AI技术和区块链安全研究，为您提供最可靠的安全检测服务
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {technologies.map((tech, index) => (
              <div key={index} className="group text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-xl shadow-blue-500/25">
                  <tech.icon className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-3 group-hover:text-cyan-400 transition-colors">
                  {tech.name}
                </h3>
                <p className="text-slate-400 text-sm">
                  {tech.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-24 bg-gradient-to-b from-white to-slate-50">
        <div className="section-container">
          <div className="text-center mb-20">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl mb-6">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-5xl font-bold text-slate-900 mb-6">
              核心服务
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
              专业的区块链安全检测服务，从代码审计到实时监控，全方位保障您的项目安全
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="group relative bg-white p-10 rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 border border-slate-100 overflow-hidden">
                {/* Background Pattern */}
                <div className="absolute top-0 right-0 w-32 h-32 opacity-5">
                  <div className={`w-full h-full bg-gradient-to-br ${service.color} rounded-full blur-2xl`}></div>
                </div>

                {/* Icon */}
                <div className={`relative w-16 h-16 bg-gradient-to-br ${service.color} rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <service.icon className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <div className="relative">
                  <h3 className="text-2xl font-bold text-slate-900 mb-4 group-hover:text-slate-800">
                    {service.title}
                  </h3>
                  <p className="text-slate-600 mb-8 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features List */}
                  <ul className="space-y-4 mb-8">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-slate-700">
                        <div className="w-6 h-6 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                          <CheckCircle className="w-4 h-4 text-white" />
                        </div>
                        <span className="font-medium">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {/* CTA Button */}
                  <button className={`w-full bg-gradient-to-r ${service.color} text-white font-semibold py-4 px-6 rounded-xl hover:shadow-lg transition-all duration-300 group-hover:scale-105`}>
                    了解更多
                    <ArrowRight className="inline-block ml-2 w-4 h-4" />
                  </button>
                </div>

                {/* Hover Effect */}
                <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl`}></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-slate-900">
        <div className="section-container">
          <div className="text-center mb-20">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-2xl mb-6">
              <Star className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-5xl font-bold text-white mb-6">
              客户评价
            </h2>
            <p className="text-xl text-slate-400 max-w-3xl mx-auto">
              来自全球区块链项目的信任与认可
            </p>
          </div>

          <div className="relative max-w-4xl mx-auto">
            {/* Testimonial Carousel */}
            <div className="bg-white rounded-3xl p-12 shadow-2xl">
              <div className="text-center">
                {/* Quote Icon */}
                <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-8">
                  <span className="text-3xl text-white">"</span>
                </div>

                {/* Testimonial Content */}
                <blockquote className="text-2xl text-slate-700 font-medium leading-relaxed mb-8">
                  {testimonials[currentTestimonial].content}
                </blockquote>

                {/* Author Info */}
                <div className="flex items-center justify-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-slate-600 to-slate-800 rounded-full flex items-center justify-center text-2xl">
                    {testimonials[currentTestimonial].avatar}
                  </div>
                  <div className="text-left">
                    <h4 className="text-xl font-bold text-slate-900">
                      {testimonials[currentTestimonial].name}
                    </h4>
                    <p className="text-slate-600">
                      {testimonials[currentTestimonial].role}
                    </p>
                    <p className="text-sm text-slate-500">
                      {testimonials[currentTestimonial].company}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Dots */}
            <div className="flex justify-center space-x-3 mt-8">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentTestimonial
                      ? 'bg-cyan-400 scale-125'
                      : 'bg-slate-600 hover:bg-slate-500'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-cyan-600 via-blue-700 to-indigo-800 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>

        {/* Floating Elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-float"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-float" style={{animationDelay: '2s'}}></div>

        <div className="section-container relative z-10">
          <div className="text-center">
            {/* Icon */}
            <div className="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-2xl mb-8 backdrop-blur-sm">
              <Shield className="w-10 h-10 text-white" />
            </div>

            {/* Heading */}
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
              准备开始保护您的
              <br />
              <span className="bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent">区块链项目</span>？
            </h2>

            {/* Description */}
            <p className="text-xl text-cyan-100 mb-12 max-w-3xl mx-auto leading-relaxed">
              立即体验ChainHawk的专业安全检测服务，运用AI驱动的智能分析技术，
              <br className="hidden md:block" />
              让您的项目在安全的天空中自由翱翔
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <button className="group bg-white text-slate-900 hover:bg-slate-100 font-bold text-lg py-4 px-10 rounded-xl transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                <span className="flex items-center">
                  免费试用
                  <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </span>
              </button>
              <button className="border-2 border-white/50 text-white hover:bg-white hover:text-slate-900 font-semibold text-lg py-4 px-10 rounded-xl transition-all duration-300 backdrop-blur-sm">
                联系专家
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="mt-16 pt-8 border-t border-white/20">
              <p className="text-cyan-200 text-sm mb-4">已获得全球1000+项目信任</p>
              <div className="flex justify-center items-center space-x-8 opacity-60">
                <div className="text-white font-semibold">DeFi</div>
                <div className="w-1 h-1 bg-white rounded-full"></div>
                <div className="text-white font-semibold">NFT</div>
                <div className="w-1 h-1 bg-white rounded-full"></div>
                <div className="text-white font-semibold">GameFi</div>
                <div className="w-1 h-1 bg-white rounded-full"></div>
                <div className="text-white font-semibold">Web3</div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home