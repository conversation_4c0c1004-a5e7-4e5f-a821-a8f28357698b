import React, { useState, useEffect } from 'react'
import {
  ArrowRight, Shield, Eye, Zap, Users, CheckCircle, Star,
  Code, Search, AlertTriangle, TrendingUp, Award, Clock,
  Globe, Lock, Cpu, Database, FileText, BarChart3, Monitor, Palette
} from 'lucide-react'

const Home = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0)
  const [theme, setTheme] = useState('modern') // 'modern' or 'terminal'

  const features = [
    {
      icon: Eye,
      title: '智能扫描',
      description: 'AI驱动的代码分析，如鹰眼般敏锐发现潜在安全隐患',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Zap,
      title: '极速检测',
      description: '毫秒级响应，快速识别威胁并提供精确定位',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Shield,
      title: '深度防护',
      description: '多层次安全分析，提供专业修复建议和最佳实践',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Users,
      title: '团队协作',
      description: '支持团队协作，实时共享安全报告和修复进度',
      color: 'from-purple-500 to-pink-500'
    }
  ]

  const stats = [
    { number: '1000+', label: '审计项目', icon: FileText },
    { number: '50K+', label: '发现漏洞', icon: AlertTriangle },
    { number: '99.9%', label: '准确率', icon: Award },
    { number: '24/7', label: '监控服务', icon: Clock }
  ]

  const technologies = [
    { name: 'Solidity', icon: Code, description: '智能合约静态分析' },
    { name: 'Rust', icon: Cpu, description: '高性能检测引擎' },
    { name: 'AI/ML', icon: Search, description: '机器学习威胁识别' },
    { name: 'Cloud', icon: Globe, description: '云原生架构' }
  ]

  const testimonials = [
    {
      name: '张伟',
      role: 'DeFi项目技术总监',
      company: 'CryptoFinance',
      content: 'ChainHawk帮助我们发现了17个关键安全漏洞，避免了潜在的重大损失。专业、高效、值得信赖！',
      avatar: '👨‍💻'
    },
    {
      name: '李小雨',
      role: '区块链安全专家',
      company: 'BlockSafe',
      content: '作为安全专家，我强烈推荐ChainHawk。它的检测精度和报告质量都达到了行业顶尖水平。',
      avatar: '👩‍💼'
    },
    {
      name: '王强',
      role: 'NFT平台创始人',
      company: 'ArtChain',
      content: '使用ChainHawk后，我们的智能合约安全性得到了显著提升，用户信任度也大幅增加。',
      avatar: '🧑‍🎨'
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  const services = [
    {
      title: '智能合约审计',
      description: '全面的智能合约代码审计服务，识别潜在的安全漏洞和逻辑错误',
      features: ['静态代码分析', '动态测试', '形式化验证', '详细报告'],
      icon: Code,
      color: 'from-blue-600 to-blue-800'
    },
    {
      title: '实时监控',
      description: '7x24小时实时监控您的智能合约，及时发现异常行为',
      features: ['实时告警', '异常检测', '性能监控', '风险评估'],
      icon: BarChart3,
      color: 'from-green-600 to-green-800'
    },
    {
      title: '安全咨询',
      description: '专业的区块链安全咨询服务，为您的项目提供全方位安全指导',
      features: ['架构设计', '最佳实践', '风险评估', '培训服务'],
      icon: Shield,
      color: 'from-purple-600 to-purple-800'
    }
  ]

  // 现代风格组件
  const ModernHome = () => (
    <div className="overflow-hidden">
      {/* Hero Section - Split Screen Design */}
      <section className="relative min-h-screen bg-black">
        {/* Left Side - Content */}
        <div className="absolute inset-0 grid grid-cols-1 lg:grid-cols-2">
          {/* Content Panel */}
          <div className="relative z-20 flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 p-8 lg:p-16">
            {/* Floating UI Elements */}
            <div className="absolute top-8 right-8 w-3 h-3 bg-cyan-400 rounded-full animate-pulse"></div>
            <div className="absolute bottom-8 left-8 w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>

            <div className="max-w-lg">
              {/* Floating Logo */}
              <div className="relative mb-8">
                <div className="absolute -top-4 -left-4 w-16 h-16 bg-cyan-400/20 rounded-2xl blur-xl"></div>
                <div className="relative w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-2xl flex items-center justify-center shadow-2xl">
                  <Shield className="w-8 h-8 text-white" />
                </div>
              </div>

              {/* Title with Creative Typography */}
              <div className="mb-8">
                <div className="text-sm text-cyan-400 font-mono mb-2 tracking-wider">// BLOCKCHAIN_SECURITY</div>
                <h1 className="text-5xl lg:text-6xl font-black text-white leading-none mb-4">
                  Chain
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 animate-pulse">
                    Hawk
                  </span>
                </h1>
                <div className="flex items-center gap-2 text-slate-400">
                  <div className="w-8 h-px bg-gradient-to-r from-cyan-400 to-transparent"></div>
                  <span className="text-sm font-mono">AI-POWERED SECURITY</span>
                </div>
              </div>

              {/* Description */}
              <p className="text-lg text-slate-300 mb-8 leading-relaxed">
                如鹰眼般敏锐的AI驱动安全检测，
                <span className="text-cyan-400 font-semibold">实时守护</span>
                您的区块链项目安全
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <button className="border border-slate-600 text-slate-300 hover:text-white hover:border-cyan-400 font-semibold py-4 px-8 rounded-xl transition-all duration-300">
                  查看演示
                </button>
                <button className="border border-slate-600 text-slate-300 hover:text-white hover:border-purple-400 font-semibold py-4 px-8 rounded-xl transition-all duration-300">
                  了解更多
                </button>
              </div>

              {/* Mini Stats */}
              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="group cursor-pointer">
                  <div className="text-2xl font-bold text-cyan-400 group-hover:scale-110 transition-transform">1000+</div>
                  <div className="text-xs text-slate-500 uppercase tracking-wider">项目</div>
                </div>
                <div className="group cursor-pointer">
                  <div className="text-2xl font-bold text-blue-400 group-hover:scale-110 transition-transform">50K+</div>
                  <div className="text-xs text-slate-500 uppercase tracking-wider">漏洞</div>
                </div>
                <div className="group cursor-pointer">
                  <div className="text-2xl font-bold text-purple-400 group-hover:scale-110 transition-transform">99.9%</div>
                  <div className="text-xs text-slate-500 uppercase tracking-wider">准确率</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Visual */}
          <div className="relative bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
            {/* Animated Background */}
            <div className="absolute inset-0">
              <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

              {/* Floating Geometric Shapes */}
              <div className="absolute top-1/4 left-1/4 w-32 h-32 border border-cyan-400/20 rounded-2xl rotate-12 animate-float"></div>
              <div className="absolute top-1/2 right-1/4 w-24 h-24 border border-purple-400/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
              <div className="absolute bottom-1/4 left-1/3 w-20 h-20 border border-blue-400/20 rotate-45 animate-float" style={{animationDelay: '4s'}}></div>
            </div>

            {/* Central Visual Element */}
            <div className="relative z-10">
              <div className="relative w-80 h-80">
                {/* Main Circle */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-cyan-400/20 to-purple-400/20 backdrop-blur-xl border border-white/10 animate-pulse"></div>

                {/* Orbiting Elements */}
                <div className="absolute inset-0 animate-spin" style={{animationDuration: '20s'}}>
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-cyan-400 rounded-full"></div>
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-3 h-3 bg-purple-400 rounded-full"></div>
                  <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-blue-400 rounded-full"></div>
                  <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-pink-400 rounded-full"></div>
                </div>

                {/* Center Icon */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-2xl">
                    <Eye className="w-10 h-10 text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section - Bento Grid Layout */}
      <section className="py-24 bg-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-cyan-100 to-blue-100 rounded-full blur-3xl opacity-30"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full blur-3xl opacity-30"></div>

        <div className="section-container relative z-10">
          {/* Section Header */}
          <div className="mb-16">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-mono text-slate-500 uppercase tracking-wider">CORE_FEATURES</span>
            </div>
            <h2 className="text-4xl lg:text-6xl font-black text-slate-900 mb-4">
              为什么选择
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-600 to-purple-600">
                ChainHawk
              </span>
            </h2>
          </div>

          {/* Bento Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-auto lg:h-[600px]">
            {/* Large Feature Card */}
            <div className="lg:col-span-2 lg:row-span-2 group relative bg-gradient-to-br from-slate-900 to-blue-900 rounded-3xl p-8 lg:p-12 overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.05%22%3E%3Ccircle%20cx%3D%2220%22%20cy%3D%2220%22%20r%3D%221%22/%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>

              {/* Floating Elements */}
              <div className="absolute top-8 right-8 w-20 h-20 border border-cyan-400/20 rounded-2xl rotate-12 animate-float"></div>
              <div className="absolute bottom-8 left-8 w-16 h-16 border border-purple-400/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>

              <div className="relative z-10 h-full flex flex-col justify-between">
                <div>
                  <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Eye className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                    AI智能扫描
                  </h3>
                  <p className="text-xl text-slate-300 leading-relaxed mb-8">
                    运用最先进的机器学习算法，如鹰眼般敏锐地识别智能合约中的安全漏洞和潜在风险
                  </p>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex -space-x-2">
                    <div className="w-8 h-8 bg-cyan-400 rounded-full border-2 border-white"></div>
                    <div className="w-8 h-8 bg-blue-400 rounded-full border-2 border-white"></div>
                    <div className="w-8 h-8 bg-purple-400 rounded-full border-2 border-white"></div>
                  </div>
                  <span className="text-sm text-slate-400">已检测 50,000+ 漏洞</span>
                </div>
              </div>
            </div>

            {/* Small Feature Cards */}
            <div className="space-y-6">
              {features.slice(1, 3).map((feature, index) => (
                <div key={index} className="group relative bg-white border border-slate-200 rounded-2xl p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className={`w-12 h-12 bg-gradient-to-br ${feature.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-slate-600 text-sm leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Hover Effect */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}></div>
                </div>
              ))}
            </div>

            {/* Bottom Feature Card */}
            <div className="lg:col-span-3 group relative bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-8 overflow-hidden">
              <div className="absolute inset-0 bg-black/20"></div>
              <div className="relative z-10 flex flex-col lg:flex-row items-center justify-between">
                <div className="flex items-center gap-6 mb-6 lg:mb-0">
                  <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-2">
                      团队协作
                    </h3>
                    <p className="text-purple-100">
                      支持团队协作，实时共享安全报告和修复进度
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white">1000+</div>
                    <div className="text-sm text-purple-200">活跃团队</div>
                  </div>
                  <div className="w-px h-12 bg-white/20"></div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white">24/7</div>
                    <div className="text-sm text-purple-200">实时监控</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technology Stack Section - Floating Cards */}
      <section className="py-24 bg-slate-900 relative overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="section-container relative z-10">
          {/* Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 mb-6">
              <Cpu className="w-6 h-6 text-cyan-400" />
              <span className="text-sm font-mono text-cyan-400 uppercase tracking-wider">TECH_STACK</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-black text-white mb-4">
              前沿技术
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                驱动创新
              </span>
            </h2>
          </div>

          {/* Floating Tech Cards */}
          <div className="relative">
            {/* Central Hub */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-br from-slate-800 to-slate-700 rounded-full border border-slate-600 flex items-center justify-center z-10">
              <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-full flex items-center justify-center">
                <Database className="w-8 h-8 text-white" />
              </div>
            </div>

            {/* Orbiting Tech Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-16">
              {technologies.map((tech, index) => (
                <div
                  key={index}
                  className="group relative"
                  style={{
                    transform: `translateY(${index % 2 === 0 ? '20px' : '-20px'})`,
                    animationDelay: `${index * 0.2}s`
                  }}
                >
                  {/* Connection Line */}
                  <div className="hidden lg:block absolute top-1/2 left-1/2 w-24 h-px bg-gradient-to-r from-slate-600 to-transparent transform -translate-y-1/2 origin-left rotate-45 opacity-30"></div>

                  {/* Tech Card */}
                  <div className="relative bg-slate-800/50 backdrop-blur-xl border border-slate-700 rounded-2xl p-6 group-hover:border-cyan-400/50 transition-all duration-300 group-hover:scale-105 group-hover:-translate-y-2">
                    {/* Glow Effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/5 to-purple-400/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    {/* Icon */}
                    <div className="relative w-16 h-16 bg-gradient-to-br from-slate-700 to-slate-600 rounded-xl flex items-center justify-center mb-4 group-hover:bg-gradient-to-br group-hover:from-cyan-500 group-hover:to-blue-600 transition-all duration-300">
                      <tech.icon className="w-8 h-8 text-slate-300 group-hover:text-white transition-colors duration-300" />
                    </div>

                    {/* Content */}
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
                      {tech.name}
                    </h3>
                    <p className="text-slate-400 text-sm leading-relaxed">
                      {tech.description}
                    </p>

                    {/* Status Indicator */}
                    <div className="flex items-center gap-2 mt-4">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-xs text-slate-500 font-mono">ACTIVE</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Bottom Stats */}
          <div className="mt-20 grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className="group cursor-pointer">
              <div className="text-3xl font-bold text-cyan-400 group-hover:scale-110 transition-transform">99.9%</div>
              <div className="text-sm text-slate-500 uppercase tracking-wider">检测精度</div>
            </div>
            <div className="group cursor-pointer">
              <div className="text-3xl font-bold text-blue-400 group-hover:scale-110 transition-transform">&lt;1s</div>
              <div className="text-sm text-slate-500 uppercase tracking-wider">响应时间</div>
            </div>
            <div className="group cursor-pointer">
              <div className="text-3xl font-bold text-purple-400 group-hover:scale-110 transition-transform">24/7</div>
              <div className="text-sm text-slate-500 uppercase tracking-wider">实时监控</div>
            </div>
            <div className="group cursor-pointer">
              <div className="text-3xl font-bold text-pink-400 group-hover:scale-110 transition-transform">AI</div>
              <div className="text-sm text-slate-500 uppercase tracking-wider">智能分析</div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Preview - Staggered Layout */}
      <section className="py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 right-0 w-64 h-64 bg-gradient-to-br from-blue-200 to-cyan-200 rounded-full blur-3xl opacity-30"></div>
          <div className="absolute bottom-1/4 left-0 w-64 h-64 bg-gradient-to-br from-purple-200 to-pink-200 rounded-full blur-3xl opacity-30"></div>
        </div>

        <div className="section-container relative z-10">
          {/* Header */}
          <div className="mb-20">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-mono text-slate-500 uppercase tracking-wider">CORE_SERVICES</span>
            </div>
            <h2 className="text-4xl lg:text-6xl font-black text-slate-900 mb-6">
              核心服务
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                全方位保障
              </span>
            </h2>
          </div>

          {/* Staggered Services */}
          <div className="space-y-24">
            {services.map((service, index) => (
              <div
                key={index}
                className={`flex flex-col lg:flex-row items-center gap-12 ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                }`}
              >
                {/* Content Side */}
                <div className="flex-1 space-y-6">
                  {/* Service Badge */}
                  <div className="inline-flex items-center gap-3 bg-white/80 backdrop-blur-sm border border-slate-200 rounded-full px-4 py-2">
                    <div className={`w-8 h-8 bg-gradient-to-br ${service.color} rounded-full flex items-center justify-center`}>
                      <service.icon className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-sm font-semibold text-slate-700">0{index + 1}</span>
                  </div>

                  {/* Title */}
                  <h3 className="text-3xl lg:text-4xl font-bold text-slate-900">
                    {service.title}
                  </h3>

                  {/* Description */}
                  <p className="text-xl text-slate-600 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-3 group">
                        <div className="w-6 h-6 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                          <CheckCircle className="w-4 h-4 text-white" />
                        </div>
                        <span className="text-slate-700 font-medium">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA */}
                  <div className="pt-4">
                    <button className={`group bg-gradient-to-r ${service.color} text-white font-semibold py-4 px-8 rounded-xl hover:shadow-xl transition-all duration-300 hover:scale-105`}>
                      <span className="flex items-center">
                        了解更多
                        <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                      </span>
                    </button>
                  </div>
                </div>

                {/* Visual Side */}
                <div className="flex-1 relative">
                  <div className="relative w-full max-w-md mx-auto">
                    {/* Main Card */}
                    <div className={`relative bg-gradient-to-br ${service.color} rounded-3xl p-8 shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500`}>
                      {/* Background Pattern */}
                      <div className="absolute inset-0 bg-white/10 rounded-3xl"></div>

                      {/* Content */}
                      <div className="relative z-10 text-white">
                        <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm">
                          <service.icon className="w-8 h-8 text-white" />
                        </div>

                        <h4 className="text-2xl font-bold mb-4">{service.title}</h4>

                        {/* Mock Dashboard */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm opacity-80">检测进度</span>
                            <span className="text-sm font-semibold">98%</span>
                          </div>
                          <div className="w-full bg-white/20 rounded-full h-2">
                            <div className="bg-white rounded-full h-2 w-[98%]"></div>
                          </div>

                          <div className="grid grid-cols-2 gap-4 mt-6">
                            <div className="text-center">
                              <div className="text-2xl font-bold">24</div>
                              <div className="text-xs opacity-80">发现问题</div>
                            </div>
                            <div className="text-center">
                              <div className="text-2xl font-bold">99.9%</div>
                              <div className="text-xs opacity-80">准确率</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Floating Elements */}
                    <div className="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full animate-bounce"></div>
                    <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section - Floating Cards */}
      <section className="py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '3s'}}></div>
        </div>

        <div className="section-container relative z-10">
          {/* Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 mb-6">
              <Star className="w-6 h-6 text-yellow-400" />
              <span className="text-sm font-mono text-yellow-400 uppercase tracking-wider">TESTIMONIALS</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-black text-white mb-4">
              客户
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">
                真实评价
              </span>
            </h2>
            <p className="text-xl text-slate-400 max-w-3xl mx-auto">
              来自全球区块链项目的信任与认可
            </p>
          </div>

          {/* Testimonials Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className={`group relative transform transition-all duration-500 hover:scale-105 ${
                  index === 1 ? 'lg:-translate-y-8' : ''
                } ${index === 2 ? 'lg:translate-y-4' : ''}`}
              >
                {/* Card */}
                <div className="relative bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 hover:bg-white/15 transition-all duration-300">
                  {/* Quote Mark */}
                  <div className="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl">
                    <span className="text-2xl text-white font-bold">"</span>
                  </div>

                  {/* Rating Stars */}
                  <div className="flex gap-1 mb-6">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>

                  {/* Content */}
                  <blockquote className="text-white text-lg leading-relaxed mb-8 font-medium">
                    {testimonial.content}
                  </blockquote>

                  {/* Author */}
                  <div className="flex items-center gap-4">
                    <div className="w-14 h-14 bg-gradient-to-br from-slate-600 to-slate-700 rounded-2xl flex items-center justify-center text-2xl border-2 border-white/20">
                      {testimonial.avatar}
                    </div>
                    <div>
                      <h4 className="text-white font-bold text-lg">
                        {testimonial.name}
                      </h4>
                      <p className="text-slate-300 text-sm">
                        {testimonial.role}
                      </p>
                      <p className="text-slate-400 text-xs">
                        {testimonial.company}
                      </p>
                    </div>
                  </div>

                  {/* Hover Glow Effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/5 to-purple-400/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                {/* Floating Elements */}
                <div className={`absolute -z-10 w-6 h-6 bg-gradient-to-br ${
                  index === 0 ? 'from-cyan-400 to-blue-500' :
                  index === 1 ? 'from-purple-400 to-pink-500' :
                  'from-yellow-400 to-orange-500'
                } rounded-full blur-sm animate-float`}
                style={{
                  top: `${20 + index * 10}%`,
                  right: `${10 + index * 5}%`,
                  animationDelay: `${index * 2}s`
                }}></div>
              </div>
            ))}
          </div>

          {/* Bottom Stats */}
          <div className="text-center">
            <div className="inline-flex items-center gap-8 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl px-8 py-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-cyan-400">4.9/5</div>
                <div className="text-sm text-slate-400">平均评分</div>
              </div>
              <div className="w-px h-8 bg-white/20"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">1000+</div>
                <div className="text-sm text-slate-400">满意客户</div>
              </div>
              <div className="w-px h-8 bg-white/20"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">98%</div>
                <div className="text-sm text-slate-400">推荐率</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section - Split Design */}
      <section className="relative min-h-screen bg-black overflow-hidden">
        {/* Split Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 min-h-screen">
          {/* Left Side - Content */}
          <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center p-8 lg:p-16">
            {/* Background Elements */}
            <div className="absolute inset-0">
              <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-cyan-500/20 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
            </div>

            <div className="relative z-10 max-w-lg">
              {/* Badge */}
              <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-8">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-white font-mono">READY_TO_START</span>
              </div>

              {/* Title */}
              <h2 className="text-4xl lg:text-5xl font-black text-white mb-6 leading-tight">
                准备开始
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                  保护项目？
                </span>
              </h2>

              {/* Description */}
              <p className="text-xl text-slate-300 mb-8 leading-relaxed">
                立即体验ChainHawk的专业安全检测服务，让AI为您的区块链项目保驾护航
              </p>

              {/* CTA Buttons */}
              <div className="space-y-4 mb-12">
                <button className="group w-full bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold text-lg py-4 px-8 rounded-xl transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                  <span className="flex items-center justify-center">
                    立即开始免费试用
                    <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </span>
                </button>

                <button className="w-full border-2 border-white/30 text-white hover:bg-white hover:text-slate-900 font-semibold text-lg py-4 px-8 rounded-xl transition-all duration-300 backdrop-blur-sm">
                  预约专家咨询
                </button>
              </div>

              {/* Features List */}
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-slate-300">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>30天免费试用</span>
                </div>
                <div className="flex items-center gap-3 text-slate-300">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>无需信用卡</span>
                </div>
                <div className="flex items-center gap-3 text-slate-300">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>24/7 专家支持</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Interactive Visual */}
          <div className="relative bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center p-8">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2240%22%20height%3D%2240%22%20viewBox%3D%220%200%2040%2040%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2220%22%20cy%3D%2220%22%20r%3D%221%22/%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>

            {/* Interactive Dashboard Mockup */}
            <div className="relative w-full max-w-md">
              {/* Main Dashboard */}
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-xl flex items-center justify-center">
                      <Shield className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-white font-bold">安全仪表板</h3>
                      <p className="text-slate-400 text-sm">实时监控</p>
                    </div>
                  </div>
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-white/5 rounded-2xl p-4 text-center">
                    <div className="text-2xl font-bold text-cyan-400">24</div>
                    <div className="text-xs text-slate-400">检测到问题</div>
                  </div>
                  <div className="bg-white/5 rounded-2xl p-4 text-center">
                    <div className="text-2xl font-bold text-green-400">99.9%</div>
                    <div className="text-xs text-slate-400">安全评分</div>
                  </div>
                </div>

                {/* Progress */}
                <div className="mb-6">
                  <div className="flex justify-between text-sm text-slate-400 mb-2">
                    <span>扫描进度</span>
                    <span>87%</span>
                  </div>
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <div className="bg-gradient-to-r from-cyan-400 to-blue-600 rounded-full h-2 w-[87%] animate-pulse"></div>
                  </div>
                </div>

                {/* Action Button */}
                <button className="w-full bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold py-3 rounded-xl hover:shadow-lg transition-all duration-300">
                  查看详细报告
                </button>
              </div>

              {/* Floating Cards */}
              <div className="absolute -top-4 -right-4 bg-yellow-400 text-black text-xs font-bold px-3 py-2 rounded-xl animate-bounce">
                新威胁检测!
              </div>
              <div className="absolute -bottom-4 -left-4 bg-green-400 text-black text-xs font-bold px-3 py-2 rounded-xl animate-pulse">
                修复完成
              </div>
            </div>

            {/* Orbiting Elements */}
            <div className="absolute inset-0 animate-spin" style={{animationDuration: '30s'}}>
              <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-cyan-400 rounded-full"></div>
              <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-purple-400 rounded-full"></div>
              <div className="absolute top-1/2 left-0 w-2 h-2 bg-pink-400 rounded-full"></div>
              <div className="absolute top-1/2 right-0 w-3 h-3 bg-blue-400 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Bottom Trust Bar */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/50 backdrop-blur-sm border-t border-white/10 py-4">
          <div className="section-container">
            <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
              <div className="text-slate-400 text-sm">
                已获得全球 <span className="text-cyan-400 font-semibold">1000+</span> 项目信任
              </div>
              <div className="flex items-center gap-8 text-slate-500 text-sm">
                <span className="hover:text-cyan-400 transition-colors cursor-pointer">DeFi</span>
                <span className="hover:text-cyan-400 transition-colors cursor-pointer">NFT</span>
                <span className="hover:text-cyan-400 transition-colors cursor-pointer">GameFi</span>
                <span className="hover:text-cyan-400 transition-colors cursor-pointer">Web3</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )

  // 命令行风格组件
  const TerminalHome = () => {
    const [currentStep, setCurrentStep] = useState(0)
    const [displayedText, setDisplayedText] = useState('')
    const [showCursor, setShowCursor] = useState(true)

    // 终端命令序列
    const terminalSteps = [
      { type: 'command', text: './chainhawk --init', delay: 1000 },
      { type: 'output', text: 'Initializing ChainHawk Security Scanner...', delay: 800 },
      { type: 'success', text: '✓ System ready', delay: 600 },
      { type: 'success', text: '✓ AI modules loaded', delay: 600 },
      { type: 'success', text: '✓ Blockchain networks connected', delay: 600 },
      { type: 'command', text: 'cat /proc/chainhawk/info', delay: 1200 },
      { type: 'command', text: 'ls -la /features/', delay: 1500 },
      { type: 'command', text: 'systemctl status chainhawk-services', delay: 1800 },
      { type: 'command', text: 'tail -f /var/log/testimonials.log', delay: 2000 },
    ]

    // 打字机效果
    useEffect(() => {
      if (currentStep < terminalSteps.length) {
        const step = terminalSteps[currentStep]
        let index = 0
        setDisplayedText('')

        const typeInterval = setInterval(() => {
          if (index < step.text.length) {
            setDisplayedText(step.text.slice(0, index + 1))
            index++
          } else {
            clearInterval(typeInterval)
            setTimeout(() => {
              setCurrentStep(prev => prev + 1)
            }, step.delay)
          }
        }, 50 + Math.random() * 50) // 随机打字速度

        return () => clearInterval(typeInterval)
      }
    }, [currentStep])

    // 光标闪烁
    useEffect(() => {
      const cursorInterval = setInterval(() => {
        setShowCursor(prev => !prev)
      }, 500)
      return () => clearInterval(cursorInterval)
    }, [])

    return (
    <div className="bg-black text-green-400 font-mono overflow-hidden min-h-screen">
      {/* Terminal Header */}
      <div className="bg-gray-900 border-b border-gray-700 px-4 py-3 flex items-center gap-2 sticky top-0 z-10">
        <div className="flex gap-2">
          <div className="w-3 h-3 bg-red-500 rounded-full hover:bg-red-400 transition-colors cursor-pointer"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full hover:bg-yellow-400 transition-colors cursor-pointer"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full hover:bg-green-400 transition-colors cursor-pointer"></div>
        </div>
        <span className="text-gray-400 text-sm ml-4">chainhawk@security:~$</span>
        <div className="ml-auto flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-green-400 text-xs">ONLINE</span>
        </div>
      </div>

      {/* Terminal Content */}
      <div className="p-6">
        {/* Boot Sequence */}
        <div className="mb-8">
          <div className="text-gray-500 text-sm mb-4">
            Last login: {new Date().toLocaleString()} on ttys001
          </div>

          {/* Animated Command Execution */}
          <div className="space-y-2">
            <div className="flex items-center">
              <span className="text-cyan-400">chainhawk@security</span>
              <span className="text-white">:</span>
              <span className="text-blue-400">~</span>
              <span className="text-white">$ </span>
              <span className="text-green-400">
                {currentStep >= 0 && terminalSteps[0] && displayedText}
                {currentStep === 0 && showCursor && <span className="bg-green-400 text-black">█</span>}
              </span>
            </div>

            {currentStep > 0 && (
              <div className="ml-4 space-y-1">
                <div className="text-gray-400 flex items-center">
                  <div className="w-4 h-4 border-2 border-cyan-400 border-t-transparent rounded-full animate-spin mr-2"></div>
                  Initializing ChainHawk Security Scanner...
                </div>
                {currentStep > 1 && (
                  <div className="text-green-400 animate-fade-in">✓ System ready</div>
                )}
                {currentStep > 2 && (
                  <div className="text-green-400 animate-fade-in" style={{animationDelay: '0.2s'}}>✓ AI modules loaded</div>
                )}
                {currentStep > 3 && (
                  <div className="text-green-400 animate-fade-in" style={{animationDelay: '0.4s'}}>✓ Blockchain networks connected</div>
                )}
              </div>
            )}

            {currentStep > 4 && (
              <div className="flex items-center mt-4">
                <span className="text-cyan-400">chainhawk@security</span>
                <span className="text-white">:</span>
                <span className="text-blue-400">~</span>
                <span className="text-white">$ </span>
                <span className="text-yellow-400">
                  {currentStep >= 5 && terminalSteps[5] && (currentStep === 5 ? displayedText : terminalSteps[5].text)}
                  {currentStep === 5 && showCursor && <span className="bg-yellow-400 text-black">█</span>}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* ASCII Art Logo with Animation */}
        {currentStep > 5 && (
          <div className="mb-8 text-cyan-400 text-sm animate-fade-in">
            <div className="mb-2 text-yellow-400">$ figlet ChainHawk</div>
            <pre className="overflow-x-auto">{`
 ██████╗██╗  ██╗ █████╗ ██╗███╗   ██╗██╗  ██╗ █████╗ ██╗    ██╗██╗  ██╗
██╔════╝██║  ██║██╔══██╗██║████╗  ██║██║  ██║██╔══██╗██║    ██║██║ ██╔╝
██║     ███████║███████║██║██╔██╗ ██║███████║███████║██║ █╗ ██║█████╔╝
██║     ██╔══██║██╔══██║██║██║╚██╗██║██╔══██║██╔══██║██║███╗██║██╔═██╗
╚██████╗██║  ██║██║  ██║██║██║ ╚████║██║  ██║██║  ██║╚███╔███╔╝██║  ██╗
 ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚══╝╚══╝ ╚═╝  ╚═╝
            `}</pre>
            <div className="text-gray-400 text-xs mt-2">
              Blockchain Security Scanner v2.1.0 | Neural-Hawk AI Engine
            </div>
          </div>
        )}

        {/* System Info with Animation */}
        {currentStep > 6 && (
          <div className="mb-8 animate-fade-in">
            <div className="flex items-center mb-2">
              <span className="text-cyan-400">chainhawk@security</span>
              <span className="text-white">:</span>
              <span className="text-blue-400">~</span>
              <span className="text-white">$ </span>
              <span className="text-yellow-400">cat /proc/chainhawk/info</span>
            </div>
            <div className="bg-gray-900 border border-gray-700 p-4 rounded ml-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="animate-slide-up" style={{animationDelay: '0.1s'}}>
                  <span className="text-gray-400">Version:</span>
                  <span className="text-white ml-2">v2.1.0</span>
                  <span className="text-green-400 ml-2 animate-pulse">●</span>
                </div>
                <div className="animate-slide-up" style={{animationDelay: '0.2s'}}>
                  <span className="text-gray-400">AI Engine:</span>
                  <span className="text-cyan-400 ml-2">Neural-Hawk v3.0</span>
                </div>
                <div className="animate-slide-up" style={{animationDelay: '0.3s'}}>
                  <span className="text-gray-400">Supported Chains:</span>
                  <span className="text-white ml-2">Ethereum, BSC, Polygon</span>
                </div>
                <div className="animate-slide-up" style={{animationDelay: '0.4s'}}>
                  <span className="text-gray-400">Detection Rate:</span>
                  <span className="text-green-400 ml-2 font-bold">99.9%</span>
                </div>
                <div className="animate-slide-up" style={{animationDelay: '0.5s'}}>
                  <span className="text-gray-400">Scanned Projects:</span>
                  <span className="text-cyan-400 ml-2 font-bold">1,000+</span>
                </div>
                <div className="animate-slide-up" style={{animationDelay: '0.6s'}}>
                  <span className="text-gray-400">Vulnerabilities Found:</span>
                  <span className="text-red-400 ml-2 font-bold">50,000+</span>
                </div>
              </div>

              {/* Real-time stats */}
              <div className="mt-4 pt-4 border-t border-gray-700">
                <div className="text-gray-400 text-xs mb-2">Real-time Statistics:</div>
                <div className="flex items-center gap-4 text-xs">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-400">CPU: 12%</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                    <span className="text-blue-400">Memory: 2.1GB</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                    <span className="text-yellow-400">Network: 45MB/s</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Features with Animation */}
        {currentStep > 7 && (
          <div className="mb-8 animate-fade-in">
            <div className="flex items-center mb-2">
              <span className="text-cyan-400">chainhawk@security</span>
              <span className="text-white">:</span>
              <span className="text-blue-400">~</span>
              <span className="text-white">$ </span>
              <span className="text-yellow-400">ls -la /features/</span>
            </div>
            <div className="ml-4">
              <div className="text-gray-400 text-sm mb-2">total {features.length}</div>
              <div className="space-y-1">
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-4 animate-slide-up hover:bg-gray-900 hover:bg-opacity-50 p-1 rounded transition-colors"
                    style={{animationDelay: `${index * 0.1}s`}}
                  >
                    <span className="text-blue-400 font-mono">drwxr-xr-x</span>
                    <span className="text-gray-400 w-12">root</span>
                    <span className="text-gray-400 w-16">security</span>
                    <span className="text-cyan-400 w-32">{feature.title.toLowerCase().replace(/\s+/g, '_')}/</span>
                    <span className="text-gray-500 flex-1">- {feature.description}</span>
                    <span className="text-green-400 text-xs">
                      {Math.floor(Math.random() * 100)}KB
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Services with Animation */}
        {currentStep > 8 && (
          <div className="mb-8 animate-fade-in">
            <div className="flex items-center mb-2">
              <span className="text-cyan-400">chainhawk@security</span>
              <span className="text-white">:</span>
              <span className="text-blue-400">~</span>
              <span className="text-white">$ </span>
              <span className="text-yellow-400">systemctl status chainhawk-services</span>
            </div>
            <div className="ml-4 space-y-3">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="bg-gray-900 border border-gray-700 p-4 rounded animate-slide-up hover:border-cyan-400 transition-colors"
                  style={{animationDelay: `${index * 0.2}s`}}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-cyan-400 font-bold">{service.title}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-green-400 text-sm font-mono">active (running)</span>
                    </div>
                  </div>
                  <div className="text-gray-400 text-sm mb-3">{service.description}</div>

                  {/* Process Info */}
                  <div className="text-xs text-gray-500 mb-2 font-mono">
                    Main PID: {1000 + index} (chainhawk-{service.title.toLowerCase().replace(/\s+/g, '-')})
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {service.features.map((feature, fIndex) => (
                      <div
                        key={fIndex}
                        className="text-sm flex items-center gap-2 animate-fade-in"
                        style={{animationDelay: `${(index * 0.2) + (fIndex * 0.1)}s`}}
                      >
                        <span className="text-green-400">✓</span>
                        <span className="text-gray-300">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Status Bar */}
                  <div className="mt-3 pt-2 border-t border-gray-700">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-500">
                        Memory: {(Math.random() * 100 + 50).toFixed(1)}MB
                      </span>
                      <span className="text-gray-500">
                        CPU: {(Math.random() * 20 + 5).toFixed(1)}%
                      </span>
                      <span className="text-gray-500">
                        Uptime: {Math.floor(Math.random() * 100 + 1)}d
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Testimonials Log with Animation */}
        {currentStep > 9 && (
          <div className="mb-8 animate-fade-in">
            <div className="flex items-center mb-2">
              <span className="text-cyan-400">chainhawk@security</span>
              <span className="text-white">:</span>
              <span className="text-blue-400">~</span>
              <span className="text-white">$ </span>
              <span className="text-yellow-400">tail -f /var/log/testimonials.log</span>
            </div>
            <div className="bg-gray-900 border border-gray-700 p-4 rounded ml-4 max-h-80 overflow-y-auto">
              <div className="text-gray-500 text-xs mb-4 font-mono">
                ==&gt; /var/log/testimonials.log &lt;==
              </div>
              {testimonials.map((testimonial, index) => (
                <div
                  key={index}
                  className="mb-4 border-b border-gray-700 pb-4 last:border-b-0 animate-slide-up"
                  style={{animationDelay: `${index * 0.3}s`}}
                >
                  <div className="text-cyan-400 text-sm font-mono">
                    [{new Date(Date.now() - (testimonials.length - index) * 86400000).toISOString().slice(0, 19)}]
                    <span className="text-green-400 ml-2">INFO</span>: New testimonial received
                  </div>
                  <div className="text-gray-300 mt-1 ml-4">"{testimonial.content}"</div>
                  <div className="text-gray-500 text-sm mt-1 ml-4">
                    -- {testimonial.name}, {testimonial.role} @ {testimonial.company}
                  </div>
                  <div className="text-blue-400 text-xs mt-1 ml-4">
                    Rating: ★★★★★ | Verified: ✓
                  </div>
                </div>
              ))}

              {/* Live indicator */}
              <div className="flex items-center gap-2 text-green-400 text-sm animate-pulse">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>Monitoring for new testimonials...</span>
              </div>
            </div>
          </div>
        )}

        {/* Interactive Command Prompt */}
        <div className="mb-8">
          <div className="flex items-center mb-2">
            <span className="text-cyan-400">chainhawk@security</span>
            <span className="text-white">:</span>
            <span className="text-blue-400">~</span>
            <span className="text-white">$ </span>
            <span className="text-yellow-400">./chainhawk --help</span>
          </div>
          <div className="bg-gray-900 border border-gray-700 p-4 rounded ml-4">
            <div className="text-gray-300 mb-4 font-mono">ChainHawk Security Scanner v2.1.0</div>
            <div className="text-gray-400 mb-4">USAGE: ./chainhawk [OPTIONS] [COMMAND]</div>

            <div className="mb-4">
              <div className="text-cyan-400 mb-2">Available Commands:</div>
              <div className="space-y-2 text-sm ml-4">
                <div className="hover:bg-gray-800 p-1 rounded transition-colors cursor-pointer">
                  <span className="text-green-400">demo</span>
                  <span className="text-gray-400 ml-8">Launch interactive security demo</span>
                </div>
                <div className="hover:bg-gray-800 p-1 rounded transition-colors cursor-pointer">
                  <span className="text-green-400">contact</span>
                  <span className="text-gray-400 ml-6">Contact security experts</span>
                </div>
                <div className="hover:bg-gray-800 p-1 rounded transition-colors cursor-pointer">
                  <span className="text-green-400">docs</span>
                  <span className="text-gray-400 ml-8">View comprehensive documentation</span>
                </div>
                <div className="hover:bg-gray-800 p-1 rounded transition-colors cursor-pointer">
                  <span className="text-green-400">scan</span>
                  <span className="text-gray-400 ml-8">Start security scan</span>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <div className="text-cyan-400 mb-2">Options:</div>
              <div className="space-y-1 text-sm ml-4">
                <div><span className="text-yellow-400">--verbose</span> <span className="text-gray-400">Enable detailed output</span></div>
                <div><span className="text-yellow-400">--config</span> <span className="text-gray-400">Specify config file</span></div>
                <div><span className="text-yellow-400">--output</span> <span className="text-gray-400">Set output format (json|xml|html)</span></div>
              </div>
            </div>

            <div className="mt-6 flex flex-wrap gap-3">
              <button className="bg-green-700 hover:bg-green-600 text-white px-4 py-2 rounded text-sm transition-all duration-200 hover:scale-105 font-mono">
                ./chainhawk demo --interactive
              </button>
              <button className="bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm transition-all duration-200 hover:scale-105 font-mono">
                ./chainhawk contact --expert
              </button>
              <button className="bg-purple-700 hover:bg-purple-600 text-white px-4 py-2 rounded text-sm transition-all duration-200 hover:scale-105 font-mono">
                ./chainhawk docs --guide
              </button>
            </div>
          </div>
        </div>

        {/* Live Terminal Footer */}
        <div className="border-t border-gray-700 pt-4">
          <div className="flex items-center mb-2">
            <span className="text-cyan-400">chainhawk@security</span>
            <span className="text-white">:</span>
            <span className="text-blue-400">~</span>
            <span className="text-white">$ </span>
            <span className="text-yellow-400">uptime</span>
          </div>
          <div className="text-gray-400 text-sm ml-4 mb-4">
            {new Date().toLocaleTimeString()} up 1000+ days, 3 users, load average: 0.12, 0.08, 0.05
          </div>

          <div className="flex items-center mb-2">
            <span className="text-cyan-400">chainhawk@security</span>
            <span className="text-white">:</span>
            <span className="text-blue-400">~</span>
            <span className="text-white">$ </span>
            <span className="text-yellow-400">systemctl status --all</span>
          </div>
          <div className="ml-4 space-y-1 text-sm">
            <div className="flex items-center gap-2">
              <span className="text-green-400">●</span>
              <span className="text-gray-300">chainhawk-scanner.service</span>
              <span className="text-green-400">active (running)</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-400">●</span>
              <span className="text-gray-300">chainhawk-ai.service</span>
              <span className="text-green-400">active (running)</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-400">●</span>
              <span className="text-gray-300">chainhawk-monitor.service</span>
              <span className="text-green-400">active (running)</span>
            </div>
          </div>

          {/* Current command prompt */}
          <div className="mt-4 flex items-center">
            <span className="text-cyan-400">chainhawk@security</span>
            <span className="text-white">:</span>
            <span className="text-blue-400">~</span>
            <span className="text-white">$ </span>
            <span className="bg-green-400 text-black animate-pulse">█</span>
          </div>
        </div>
      </div>
    </div>
  )
}

  return (
    <div className="pt-16">
      {/* Theme Switcher */}
      <div className="fixed top-20 right-4 z-50">
        <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-2 flex gap-2">
          <button
            onClick={() => setTheme('modern')}
            className={`p-2 rounded-lg transition-all duration-300 ${
              theme === 'modern'
                ? 'bg-cyan-500 text-white shadow-lg'
                : 'text-gray-400 hover:text-white hover:bg-white/10'
            }`}
            title="现代风格"
          >
            <Palette className="w-5 h-5" />
          </button>
          <button
            onClick={() => setTheme('terminal')}
            className={`p-2 rounded-lg transition-all duration-300 ${
              theme === 'terminal'
                ? 'bg-green-500 text-white shadow-lg'
                : 'text-gray-400 hover:text-white hover:bg-white/10'
            }`}
            title="命令行风格"
          >
            <Monitor className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Render based on theme */}
      {theme === 'modern' ? <ModernHome /> : <TerminalHome />}
    </div>
  )
}

export default Home